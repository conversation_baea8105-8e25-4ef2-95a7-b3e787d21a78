/**
 * Core type definitions for Arien AI CLI
 */

export interface AIProvider {
  name: string;
  baseURL: string;
  models: string[];
  apiKeyEnvVar: string;
  description: string;
}

export interface AIProviderConfig {
  provider: string;
  model: string;
  apiKey: string;
  baseURL?: string | undefined;
  temperature?: number | undefined;
  maxTokens?: number | undefined;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  provider: string;
  model: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CLIConfig {
  defaultProvider: string;
  defaultModel: string;
  temperature: number;
  maxTokens: number;
  providers: Record<string, AIProviderConfig>;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

export interface CommandOptions {
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  verbose?: boolean;
  config?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  } | undefined;
  model: string;
  provider: string;
}

export interface ProviderError extends Error {
  provider: string;
  statusCode?: number;
  details?: unknown;
}

// Predefined AI providers
export const AI_PROVIDERS: Record<string, AIProvider> = {
  openai: {
    name: 'OpenAI',
    baseURL: 'https://api.openai.com/v1',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    apiKeyEnvVar: 'OPENAI_API_KEY',
    description: 'OpenAI GPT models'
  },
  deepseek: {
    name: 'DeepSeek',
    baseURL: 'https://api.deepseek.com/v1',
    models: ['deepseek-chat', 'deepseek-reasoner'],
    apiKeyEnvVar: 'DEEPSEEK_API_KEY',
    description: 'DeepSeek AI models'
  }
};

export type ProviderName = keyof typeof AI_PROVIDERS;
