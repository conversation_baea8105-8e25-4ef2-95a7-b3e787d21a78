/**
 * Configuration management service
 */
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { config as loadEnv } from 'dotenv';
import { AI_PROVIDERS } from '../types/index.js';
// Load environment variables
loadEnv();
export class ConfigService {
    static instance;
    config;
    configPath;
    constructor() {
        this.configPath = join(homedir(), '.arien-ai', 'config.json');
        this.config = this.loadConfig();
    }
    static getInstance() {
        if (!ConfigService.instance) {
            ConfigService.instance = new ConfigService();
        }
        return ConfigService.instance;
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Update configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
        this.saveConfig();
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerName) {
        return this.config.providers[providerName];
    }
    /**
     * Set provider configuration
     */
    setProviderConfig(providerName, config) {
        this.config.providers[providerName] = config;
        this.saveConfig();
    }
    /**
     * Get API key for provider from environment or config
     */
    getApiKey(providerName) {
        const provider = AI_PROVIDERS[providerName];
        if (!provider)
            return undefined;
        // First try environment variable
        const envKey = process.env[provider.apiKeyEnvVar];
        if (envKey)
            return envKey;
        // Then try config
        const providerConfig = this.getProviderConfig(providerName);
        return providerConfig?.apiKey;
    }
    /**
     * Load configuration from file
     */
    loadConfig() {
        const defaultConfig = {
            defaultProvider: 'openai',
            defaultModel: 'gpt-4',
            temperature: 0.7,
            maxTokens: 2048,
            providers: {},
            logLevel: 'info',
        };
        if (!existsSync(this.configPath)) {
            this.ensureConfigDir();
            this.saveConfigToFile(defaultConfig);
            return defaultConfig;
        }
        try {
            const configData = readFileSync(this.configPath, 'utf-8');
            const loadedConfig = JSON.parse(configData);
            // Merge with defaults to ensure all properties exist
            return { ...defaultConfig, ...loadedConfig };
        }
        catch (error) {
            console.warn(`Failed to load config from ${this.configPath}, using defaults:`, error);
            return defaultConfig;
        }
    }
    /**
     * Save configuration to file
     */
    saveConfig() {
        this.saveConfigToFile(this.config);
    }
    saveConfigToFile(config) {
        try {
            this.ensureConfigDir();
            writeFileSync(this.configPath, JSON.stringify(config, null, 2));
        }
        catch (error) {
            console.error(`Failed to save config to ${this.configPath}:`, error);
        }
    }
    /**
     * Ensure config directory exists
     */
    ensureConfigDir() {
        const configDir = dirname(this.configPath);
        if (!existsSync(configDir)) {
            mkdirSync(configDir, { recursive: true });
        }
    }
    /**
     * Reset configuration to defaults
     */
    resetConfig() {
        this.config = {
            defaultProvider: 'openai',
            defaultModel: 'gpt-4',
            temperature: 0.7,
            maxTokens: 2048,
            providers: {},
            logLevel: 'info',
        };
        this.saveConfig();
    }
}
//# sourceMappingURL=config.js.map