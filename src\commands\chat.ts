/**
 * Chat command implementation
 */

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import boxen from 'boxen';
import { AIService } from '../services/ai.js';
import { ConfigService } from '../services/config.js';
import { logger } from '../utils/logger.js';
import type { ChatMessage, CommandOptions, AIProviderConfig } from '../types/index.js';
import { AI_PROVIDERS } from '../types/index.js';

export function createChatCommand(): Command {
  const command = new Command('chat');
  
  command
    .description('Start an interactive chat session with AI')
    .option('-p, --provider <provider>', 'AI provider to use (openai, deepseek)')
    .option('-m, --model <model>', 'Model to use')
    .option('-t, --temperature <number>', 'Temperature for responses (0-2)', parseFloat)
    .option('--max-tokens <number>', 'Maximum tokens for responses', parseInt)
    .option('-v, --verbose', 'Enable verbose logging')
    .option('-s, --system <prompt>', 'System prompt to use')
    .action(async (options: CommandOptions & { system?: string }) => {
      await runChatSession(options);
    });

  return command;
}

async function runChatSession(options: CommandOptions & { system?: string }): Promise<void> {
  const aiService = new AIService();
  const configService = ConfigService.getInstance();
  const config = configService.getConfig();

  // Set log level if verbose
  if (options.verbose) {
    configService.updateConfig({ logLevel: 'debug' });
  }

  // Display welcome message
  displayWelcome();

  // Get provider and model selection
  const { provider, model } = await getProviderAndModel(options);
  
  logger.info(`Starting chat session with ${provider}/${model}`);

  const messages: ChatMessage[] = [];
  
  // Add system prompt if provided
  if (options.system) {
    messages.push({
      role: 'system',
      content: options.system,
      timestamp: new Date(),
    });
    logger.debug('System prompt added');
  }

  console.log(chalk.green('\n🤖 Chat session started! Type "exit" to quit, "clear" to clear history.\n'));

  while (true) {
    try {
      // Get user input
      const { userInput } = await inquirer.prompt([
        {
          type: 'input',
          name: 'userInput',
          message: chalk.blue('You:'),
          validate: (input: string) => input.trim().length > 0 || 'Please enter a message',
        },
      ]);

      // Handle special commands
      if (userInput.toLowerCase() === 'exit') {
        console.log(chalk.yellow('\n👋 Goodbye!'));
        break;
      }

      if (userInput.toLowerCase() === 'clear') {
        messages.length = 0;
        if (options.system) {
          messages.push({
            role: 'system',
            content: options.system,
            timestamp: new Date(),
          });
        }
        console.log(chalk.yellow('\n🧹 Chat history cleared!\n'));
        continue;
      }

      // Add user message
      messages.push({
        role: 'user',
        content: userInput,
        timestamp: new Date(),
      });

      // Show loading spinner
      const spinner = ora('Thinking...').start();

      try {
        // Get AI response
        const chatOptions: Partial<AIProviderConfig> = {
          provider,
          model,
        };

        if (options.temperature !== undefined) {
          chatOptions.temperature = options.temperature;
        }

        if (options.maxTokens !== undefined) {
          chatOptions.maxTokens = options.maxTokens;
        }

        const response = await aiService.chat(messages, chatOptions);

        spinner.stop();

        // Add AI response to messages
        messages.push({
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
        });

        // Display response in a box
        const responseBox = boxen(response.content, {
          padding: 1,
          margin: { top: 1, bottom: 1 },
          borderStyle: 'round',
          borderColor: 'cyan',
          title: '🤖 AI Response',
          titleAlignment: 'left',
        });

        console.log(responseBox);

        // Show usage info if available
        if (response.usage && options.verbose) {
          logger.debug(`Tokens used: ${response.usage.totalTokens} (prompt: ${response.usage.promptTokens}, completion: ${response.usage.completionTokens})`);
        }

      } catch (error) {
        spinner.stop();
        logger.error('Failed to get AI response:', error);
        
        if (error instanceof Error) {
          console.log(chalk.red(`\n❌ Error: ${error.message}\n`));
        }
      }

    } catch (error) {
      if (error instanceof Error && error.message.includes('User force closed')) {
        console.log(chalk.yellow('\n👋 Goodbye!'));
        break;
      }
      logger.error('Unexpected error:', error);
    }
  }
}

async function getProviderAndModel(options: CommandOptions): Promise<{ provider: string; model: string }> {
  const configService = ConfigService.getInstance();
  const config = configService.getConfig();

  let provider = options.provider || config.defaultProvider;
  let model = options.model || config.defaultModel;

  // If provider not specified, prompt user
  if (!options.provider) {
    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: 'Select AI provider:',
        choices: Object.entries(AI_PROVIDERS).map(([key, value]) => ({
          name: `${value.name} (${value.description})`,
          value: key,
        })),
        default: config.defaultProvider,
      },
    ]);
    provider = selectedProvider;
  }

  // If model not specified, prompt user
  if (!options.model) {
    const providerInfo = AI_PROVIDERS[provider];
    if (providerInfo && providerInfo.models.length > 1) {
      const { selectedModel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedModel',
          message: 'Select model:',
          choices: providerInfo.models,
          default: providerInfo.models[0],
        },
      ]);
      model = selectedModel;
    } else if (providerInfo) {
      model = providerInfo.models[0] || model;
    }
  }

  return { provider, model };
}

function displayWelcome(): void {
  const welcome = boxen(
    chalk.bold.cyan('🚀 Welcome to Arien AI CLI\n') +
    chalk.white('A modern terminal interface powered by AI\n\n') +
    chalk.gray('Supported providers: OpenAI, DeepSeek\n') +
    chalk.gray('Type "exit" to quit, "clear" to clear history'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'double',
      borderColor: 'cyan',
      textAlignment: 'center',
    }
  );

  console.log(welcome);
}
