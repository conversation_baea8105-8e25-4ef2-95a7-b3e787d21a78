/**
 * Configuration management service
 */
import type { CLIConfig, AIProviderConfig } from '../types/index.js';
export declare class ConfigService {
    private static instance;
    private config;
    private configPath;
    private constructor();
    static getInstance(): ConfigService;
    /**
     * Get current configuration
     */
    getConfig(): CLIConfig;
    /**
     * Update configuration
     */
    updateConfig(updates: Partial<CLIConfig>): void;
    /**
     * Get provider configuration
     */
    getProviderConfig(providerName: string): AIProviderConfig | undefined;
    /**
     * Set provider configuration
     */
    setProviderConfig(providerName: string, config: AIProviderConfig): void;
    /**
     * Get API key for provider from environment or config
     */
    getApiKey(providerName: string): string | undefined;
    /**
     * Load configuration from file
     */
    private loadConfig;
    /**
     * Save configuration to file
     */
    private saveConfig;
    private saveConfigToFile;
    /**
     * Ensure config directory exists
     */
    private ensureConfigDir;
    /**
     * Reset configuration to defaults
     */
    resetConfig(): void;
}
//# sourceMappingURL=config.d.ts.map