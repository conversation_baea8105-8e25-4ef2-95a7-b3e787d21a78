#!/usr/bin/env node
/**
 * Arien AI CLI - Modern terminal interface powered by AI
 *
 * A TypeScript-based CLI tool that provides multi-provider AI chat capabilities
 * with support for OpenAI, DeepSeek, and other OpenAI-compatible providers.
 */
import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { createChatCommand } from './commands/chat.js';
import { ConfigService } from './services/config.js';
import { logger } from './utils/logger.js';
import { AI_PROVIDERS } from './types/index.js';
const program = new Command();
async function main() {
    try {
        // Initialize configuration service
        const configService = ConfigService.getInstance();
        // Display ASCII art banner
        console.log(chalk.cyan(figlet.textSync('Arien AI', {
            font: 'Standard',
            horizontalLayout: 'default',
            verticalLayout: 'default',
        })));
        // Configure main program
        program
            .name('arien')
            .description('Modern CLI terminal system powered by AI with multi-provider support')
            .version('1.0.0')
            .configureOutput({
            writeErr: (str) => process.stderr.write(chalk.red(str)),
            writeOut: (str) => process.stdout.write(str),
        });
        // Add chat command
        program.addCommand(createChatCommand());
        // Add config command
        const configCommand = new Command('config');
        configCommand
            .description('Manage configuration settings')
            .option('--show', 'Show current configuration')
            .option('--reset', 'Reset configuration to defaults')
            .option('--set-provider <provider>', 'Set default provider')
            .option('--set-model <model>', 'Set default model')
            .option('--set-api-key <provider> <key>', 'Set API key for provider')
            .action(async (options) => {
            await handleConfigCommand(options);
        });
        program.addCommand(configCommand);
        // Add providers command
        const providersCommand = new Command('providers');
        providersCommand
            .description('List available AI providers')
            .action(() => {
            console.log(chalk.bold('\n📡 Available AI Providers:\n'));
            Object.entries(AI_PROVIDERS).forEach(([key, provider]) => {
                console.log(chalk.cyan(`${provider.name} (${key})`));
                console.log(chalk.gray(`  Description: ${provider.description}`));
                console.log(chalk.gray(`  Base URL: ${provider.baseURL}`));
                console.log(chalk.gray(`  Models: ${provider.models.join(', ')}`));
                console.log(chalk.gray(`  API Key Env: ${provider.apiKeyEnvVar}`));
                console.log();
            });
        });
        program.addCommand(providersCommand);
        // Parse command line arguments
        await program.parseAsync();
    }
    catch (error) {
        logger.error('Application error:', error);
        process.exit(1);
    }
}
async function handleConfigCommand(options) {
    const configService = ConfigService.getInstance();
    if (options.show) {
        const config = configService.getConfig();
        console.log(chalk.bold('\n⚙️  Current Configuration:\n'));
        console.log(chalk.cyan('Default Provider:'), config.defaultProvider);
        console.log(chalk.cyan('Default Model:'), config.defaultModel);
        console.log(chalk.cyan('Temperature:'), config.temperature);
        console.log(chalk.cyan('Max Tokens:'), config.maxTokens);
        console.log(chalk.cyan('Log Level:'), config.logLevel);
        console.log(chalk.bold('\n🔑 Provider Configurations:'));
        Object.entries(config.providers).forEach(([provider, providerConfig]) => {
            console.log(chalk.yellow(`  ${provider}:`));
            console.log(chalk.gray(`    Model: ${providerConfig.model}`));
            console.log(chalk.gray(`    Base URL: ${providerConfig.baseURL || 'default'}`));
            console.log(chalk.gray(`    API Key: ${providerConfig.apiKey ? '***configured***' : 'not set'}`));
        });
        console.log();
        return;
    }
    if (options.reset) {
        configService.resetConfig();
        logger.success('Configuration reset to defaults');
        return;
    }
    if (options.setProvider) {
        if (!AI_PROVIDERS[options.setProvider]) {
            logger.error(`Unknown provider: ${options.setProvider}`);
            return;
        }
        configService.updateConfig({ defaultProvider: options.setProvider });
        logger.success(`Default provider set to: ${options.setProvider}`);
        return;
    }
    if (options.setModel) {
        configService.updateConfig({ defaultModel: options.setModel });
        logger.success(`Default model set to: ${options.setModel}`);
        return;
    }
    // If no specific action, show help
    console.log(chalk.yellow('Use --show to view current configuration'));
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});
// Start the application
main().catch((error) => {
    logger.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map