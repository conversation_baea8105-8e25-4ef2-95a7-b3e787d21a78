/**
 * Core type definitions for Arien AI CLI
 */
export interface AIProvider {
    name: string;
    baseURL: string;
    models: string[];
    apiKeyEnvVar: string;
    description: string;
}
export interface AIProviderConfig {
    provider: string;
    model: string;
    apiKey: string;
    baseURL?: string | undefined;
    temperature?: number | undefined;
    maxTokens?: number | undefined;
}
export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    timestamp?: Date;
}
export interface ChatSession {
    id: string;
    messages: ChatMessage[];
    provider: string;
    model: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CLIConfig {
    defaultProvider: string;
    defaultModel: string;
    temperature: number;
    maxTokens: number;
    providers: Record<string, AIProviderConfig>;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
}
export interface CommandOptions {
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    verbose?: boolean;
    config?: string;
}
export interface AIResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    } | undefined;
    model: string;
    provider: string;
}
export interface ProviderError extends Error {
    provider: string;
    statusCode?: number;
    details?: unknown;
}
export declare const AI_PROVIDERS: Record<string, AIProvider>;
export type ProviderName = keyof typeof AI_PROVIDERS;
//# sourceMappingURL=index.d.ts.map