# Arien AI CLI Setup Guide

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Environment Variables
```bash
cp .env.example .env
```

Edit `.env` and add your API keys:
```bash
# For OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# For DeepSeek
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

### 3. Development Mode
```bash
npm run dev chat
```

### 4. Production Build
```bash
npm run build
npm start chat
```

## Getting API Keys

### OpenAI
1. Visit https://platform.openai.com/api-keys
2. Create a new API key
3. Add it to your `.env` file as `OPENAI_API_KEY`

### DeepSeek
1. Visit https://platform.deepseek.com/api_keys
2. Create a new API key
3. Add it to your `.env` file as `DEEPSEEK_API_KEY`

## Usage Examples

### Basic Chat
```bash
npm run dev chat
```

### Specify Provider and Model
```bash
npm run dev chat --provider deepseek --model deepseek-chat
```

### With Custom Settings
```bash
npm run dev chat --provider openai --model gpt-4 --temperature 0.8 --max-tokens 1000
```

### With System Prompt
```bash
npm run dev chat --system "You are a helpful coding assistant"
```

### View Available Providers
```bash
npm run dev providers
```

### Configuration Management
```bash
# Show current config
npm run dev config --show

# Set default provider
npm run dev config --set-provider deepseek

# Reset to defaults
npm run dev config --reset
```

## Testing

### Run Tests
```bash
npm test
```

### Run Linting
```bash
npm run lint
```

### Format Code
```bash
npm run format
```

## Architecture Overview

- **TypeScript 5.8** with strict mode enabled
- **Node.js 22** with ES modules
- **OpenAI SDK 4.77.0** for AI provider communication
- **Commander.js** for CLI framework
- **Inquirer.js** for interactive prompts
- **Chalk** for colored terminal output
- **Structured architecture** with services, providers, and commands

## Adding New Providers

To add a new OpenAI-compatible provider:

1. Add to `src/types/index.ts`:
```typescript
newprovider: {
  name: 'New Provider',
  baseURL: 'https://api.newprovider.com/v1',
  models: ['model-1', 'model-2'],
  apiKeyEnvVar: 'NEWPROVIDER_API_KEY',
  description: 'New AI provider'
}
```

2. The provider will automatically be available in the CLI!

## Troubleshooting

### "No API key found" Error
- Make sure you've set the appropriate environment variable
- Check that your `.env` file is in the project root
- Verify the API key is valid

### TypeScript Compilation Errors
- Run `npm run build` to check for type errors
- Ensure all dependencies are installed with `npm install`

### Module Resolution Issues
- Make sure you're using Node.js 22 or higher
- Check that `"type": "module"` is in package.json

## Development

### Project Structure
```
src/
├── commands/          # CLI command implementations
├── providers/         # AI provider configurations
├── services/          # Core services (AI, config)
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
└── index.ts           # Main entry point
```

### Key Features
- ✅ Multi-provider AI support (OpenAI, DeepSeek)
- ✅ TypeScript 5.8 with strict type checking
- ✅ Modern ES modules
- ✅ Interactive CLI with colors and spinners
- ✅ Configuration management
- ✅ Comprehensive testing
- ✅ Extensible architecture
- ✅ Error handling and logging
